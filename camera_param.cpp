#include "camera_param.h"
#include <QDir>
#include <QRegExp>
#include <QFile>
#include <QTextStream>
#include <algorithm>
#include <dirent.h>
#include <QProcess>
#include <xkbcommon/xkbcommon.h>
#include <QRegularExpression>
// 构造函数
CameraParams::CameraParams(QObject *parent) : QObject(parent)
{
    // 初始化
}

// 析构函数
CameraParams::~CameraParams()
{
    // 清理资源
}

bool CameraParams::setControl(const QString &devicePath, int controlId, int value)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {

        return false;
    }

    // 打开摄像头设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {
        return false;
    }

    // 首先获取控制范围
    struct v4l2_queryctrl queryctrl;
    memset(&queryctrl, 0, sizeof(queryctrl));
    queryctrl.id = controlId;

    if (ioctl(fd, VIDIOC_QUERYCTRL, &queryctrl) < 0) {
        qWarning("get queryctrl error: %s", strerror(errno));
        ::close(fd);
        return false;
    }

    // 确保值在范围内
    int min = queryctrl.minimum;
    int max = queryctrl.maximum;

    // 限制值在该设备支持的范围内
    if (value < min) value = min;
    if (value > max) value = max;

    // 设置控制参数
    struct v4l2_control control;
    memset(&control, 0, sizeof(control));
    control.id = controlId;
    control.value = value;

    bool success = true;
    if (ioctl(fd, VIDIOC_S_CTRL, &control) < 0)
    {
        //qWarning("set control error: %s", strerror(errno));
        success = false;
    }

    // 关闭设备
    ::close(fd);
    return success;
}
bool CameraParams::getdefault(const QString &devicePath, int controlId, int &min, int &max, int &step, int &defaultValue)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 打开摄像头设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {
        qWarning("no open %s: %s", qPrintable(devicePath), strerror(errno));
        return false;
    }

    // 获取控制范围
    struct v4l2_queryctrl queryctrl;
    memset(&queryctrl, 0, sizeof(queryctrl));
    queryctrl.id = controlId;

    bool success = true;
    if (ioctl(fd, VIDIOC_QUERYCTRL, &queryctrl) < 0)
    {
        qWarning("get_error: %s", strerror(errno));
        success = false;
    }
    else
    {
        min = queryctrl.minimum;
        max = queryctrl.maximum;
        step = queryctrl.step;
        defaultValue = queryctrl.default_value;

        // qDebug() << "控制范围: " << min << " 到 " << max
        //          << ", 步长: " << step << ", 默认值: " << defaultValue;
    }
    // 关闭设备
    ::close(fd);
    return success;
}

bool CameraParams::getControlRange(const QString &devicePath, int controlId, int &min, int &max, int &step, int &defaultValue)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 打开摄像头设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {
        return false;
    }

    // 获取控制范围
    struct v4l2_queryctrl queryctrl;
    memset(&queryctrl, 0, sizeof(queryctrl));
    queryctrl.id = controlId;

    bool success = true;
    if (ioctl(fd, VIDIOC_QUERYCTRL, &queryctrl) < 0)
    {
        // 如果查询失败，返回false
        success = false;
        ::close(fd);
        return false; // 提前返回，因为无法获取控制范围
    }
    else
    {
        min = queryctrl.minimum;
        max = queryctrl.maximum;
        step = queryctrl.step;
        defaultValue = queryctrl.default_value;
    }

    // 使用 v4l2-ctl 命令获取设备当前值
    QString program = "v4l2-ctl";
    QStringList arguments = QStringList() << "-d" << devicePath << "--get-ctrl=" + getControlName(controlId);
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished();

    // 解析命令输出获取当前值
    QString output = process.readAllStandardOutput();
    QRegExp rx(getControlName(controlId) + ":\\s*(-?\\d+)");
    if (rx.indexIn(output) != -1)
    {
        bool ok;
        defaultValue = rx.cap(1).toInt(&ok);
        if (!ok)
        {
            qWarning("get value error");
            success = false;
        }
    }
    else
    {
        success = false; // 找不到值时返回false
    }

    // 关闭设备
    ::close(fd);
    return success;
}

QString CameraParams::getControlName(int controlId)
{
    switch (controlId)
    {
    case V4L2_CID_BRIGHTNESS:
        return "brightness";
    case V4L2_CID_CONTRAST:
        return "contrast";
    case V4L2_CID_SATURATION:
        return "saturation";
    case V4L2_CID_HUE:
        return "hue";
    case V4L2_CID_SHARPNESS:
        return "sharpness";
    case V4L2_CID_GAMMA:
        return "gamma";
    case V4L2_CID_BACKLIGHT_COMPENSATION:
        return "backlight_compensation";
    case V4L2_CID_GAIN:
        return "gain";
    case V4L2_CID_WHITE_BALANCE_TEMPERATURE:
        return "white_balance_temperature";
    case V4L2_CID_AUTO_WHITE_BALANCE:
        return "white_balance_temperature_auto";
    case V4L2_CID_EXPOSURE_ABSOLUTE:
        return "exposure_absolute";
    case V4L2_CID_EXPOSURE_AUTO:
        return "exposure_auto";
    case V4L2_CID_ZOOM_ABSOLUTE:
        return "zoom_absolute";
    case V4L2_CID_PAN_ABSOLUTE:
        return "pan_absolute";
    case V4L2_CID_TILT_ABSOLUTE:
        return "tilt_absolute";
    default:
        qWarning("未知的控制 ID: %d", controlId);
        return "";
    }
}

// 获取自动白平衡状态
bool CameraParams::getAutoWhiteBalance(const QString &devicePath, bool &isAuto)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 使用 v4l2-ctl 命令获取自动白平衡状态
    QString program = "v4l2-ctl";
    QStringList arguments = QStringList() << "-d" << devicePath << "--get-ctrl=white_balance_temperature_auto";
    QProcess process;
    process.start(program, arguments);
    process.waitForFinished();

    // 解析命令输出获取当前值
    QString output = process.readAllStandardOutput();
    QRegExp rx("white_balance_temperature_auto:\\s*(\\d+)");
    if (rx.indexIn(output) != -1)
    {
        bool ok;
        int value = rx.cap(1).toInt(&ok);
        if (ok)
        {
            isAuto = (value == 1);
            qDebug() << "auto WhiteBalance: " << (isAuto ? "open" : "close");
            return true;
        }
    }

    qWarning("no find auto WhiteBalance");
    return false; // 找不到值时返回false
}

// 设置自动白平衡状态
bool CameraParams::setAutoWhiteBalance(const QString &devicePath, bool isAuto)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 打开摄像头设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {
        qWarning("no open  %s: %s", qPrintable(devicePath), strerror(errno));
        return false;
    }

    // 设置自动白平衡状态
    struct v4l2_control control;
    memset(&control, 0, sizeof(control));
    control.id = V4L2_CID_AUTO_WHITE_BALANCE;
    control.value = isAuto ? 1 : 0;

    bool success = true;
    if (ioctl(fd, VIDIOC_S_CTRL, &control) < 0)
    {
        qWarning("setAutoWhiteBalance error: %s", strerror(errno));
        success = false;
    }
    else
    {
        //qDebug() << "setAutoWhiteBalance success";
    }

    // 关闭设备
    ::close(fd);
    return success;
}

// 获取自动曝光状态
bool CameraParams::getAutoExposure(const QString &devicePath, bool &isAuto)
{
    if (devicePath.isEmpty()) {
        return false;
    }

    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0) {
        return false;
    }

    struct v4l2_queryctrl queryctrl;
    memset(&queryctrl, 0, sizeof(queryctrl));
    queryctrl.id = V4L2_CID_EXPOSURE_AUTO;

    if (ioctl(fd, VIDIOC_QUERYCTRL, &queryctrl) < 0) {
        qWarning() << "get AutoExposure error:" << strerror(errno);
        close(fd);
        return false;
    }

    close(fd);

    // 使用v4l2-ctl命令获取自动曝光状态
    QProcess process;
    process.start("v4l2-ctl", QStringList() << "-d" << devicePath << "--get-ctrl=exposure_auto");
    process.waitForFinished();

    if (process.exitCode() != 0) {
        qWarning() << "getAutoExposure error:" << process.errorString();
        return false;
    }

    QString output = process.readAllStandardOutput();
    QRegExp rx("exposure_auto: (\\d+)");
    if (rx.indexIn(output) != -1) {
        int value = rx.cap(1).toInt();
        isAuto = (value == queryctrl.default_value);
        qDebug() << "getAutoExposure:" << (isAuto ? "auto" : "manual") << "value:" << value;
        return true;
    }

    qWarning() << "no find auto exposure value";
    return false; // 找不到值时返回false
}

// 设置自动曝光状态
bool CameraParams::setAutoExposure(const QString &devicePath, bool isAuto)
{
    if (devicePath.isEmpty()) {
        qWarning() << "open error";
        return false;
    }

    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0) {
        qDebug() << "autoexposurefd < 0";
        return false;
    }

    struct v4l2_queryctrl queryctrl;
    memset(&queryctrl, 0, sizeof(queryctrl));
    queryctrl.id = V4L2_CID_EXPOSURE_AUTO;

    if (ioctl(fd, VIDIOC_QUERYCTRL, &queryctrl) < 0) {
        qDebug() << "autoexposurefd < 088888888888888";
        qWarning() << "getAutoExposure error:" << strerror(errno);
        close(fd);
        return false;
    }

    struct v4l2_control control;
    memset(&control, 0, sizeof(control));
    control.id = V4L2_CID_EXPOSURE_AUTO;
    control.value = isAuto ? queryctrl.default_value : (queryctrl.default_value == 3 ? 1 : 2);

    qDebug() << "setAutoExposure:" << (isAuto ? "auto" : "manual") << "value:" << control.value;

    bool result = (ioctl(fd, VIDIOC_S_CTRL, &control) == 0);
    if (!result) {
        qWarning() << "setAutoExposure error:" << strerror(errno);
    }

    close(fd);
    qDebug() << "autoexposurefd < 0*********************"<<result;
    return result;
}

// 获取自动焦点状态
bool CameraParams::getAutoFocus(const QString &devicePath, bool &isAuto)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 使用v4l2-ctl命令获取自动焦点状态
    QProcess process;
    process.start("v4l2-ctl", QStringList() << "-d" << devicePath << "--get-ctrl=focus_auto");
    process.waitForFinished();

    if (process.exitCode() != 0)
    {
        qWarning() << "getAutoFocus error:" << process.errorString();
        return false;
    }

    QString output = process.readAllStandardOutput();
    QRegExp rx("focus_auto: (\\d+)");

    if (rx.indexIn(output) != -1)
    {
        int value = rx.cap(1).toInt();

        // 根据当前值判断是否为自动模式
        // 如果值为1，则为自动模式
        isAuto = (value == 1);
        qDebug() << "current :" << value << (isAuto ? "auto" : "manually");
        return true;
    }

    qWarning() << "no find auto focus value";
    return false; // 找不到值时返回false
}

// 设置自动焦点状态
bool CameraParams::setAutoFocus(const QString &devicePath, bool isAuto)
{
    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {
        return false;
    }

    // 打开摄像头设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {
        qWarning() << "no open error:" << devicePath;
        return false;
    }

    // 设置自动焦点状态
    struct v4l2_control control;
    memset(&control, 0, sizeof(control));
    control.id = V4L2_CID_FOCUS_AUTO;
    // 1 表示自动模式
    // 0 表示手动模式
    control.value = isAuto ? 1 : 0;

    qDebug() << "set Focus:" << (isAuto ? "auto" : "manually") << "值:" << control.value;

    bool result = (ioctl(fd, VIDIOC_S_CTRL, &control) == 0);
    if (!result)
    {
        qWarning() << "set Focus error:" << strerror(errno);
    }

    close(fd);
    return result;
}

// 查找视频设备
QList<QPair<QString, QString>> CameraParams::findVideoDevices()
{
    QList<QPair<QString, QString>> devices; // <设备名称, 设备路径>
    QList<QPair<int, QString>> tempDevices; // <设备号, 设备名称> 用于排序

    // 打开/dev目录
    DIR *dir;
    struct dirent *ent;

    if ((dir = opendir("/dev")) != nullptr)
    {
        // 遍历/dev目录下的所有文件
        while ((ent = readdir(dir)) != nullptr)
        {
            // 检查是否是video设备
            if (strncmp(ent->d_name, "video", 5) == 0)
            {
                // 提取设备号
                QString deviceStr = QString(ent->d_name).mid(5); // 获取"video"后面的部分
                bool ok;
                int deviceNumber = deviceStr.toInt(&ok);

                // 从video11开始，只处理奇数设备
                if (ok && deviceNumber >= 11 && (deviceNumber % 2 != 0))
                {
                    QString devicePath = "/dev/" + QString(ent->d_name);
                    int fd = open(devicePath.toUtf8().constData(), O_RDONLY);

                    if (fd >= 0)
                    {
                        // 检查设备是否是视频捕获设备
                        struct v4l2_capability cap;
                        if (ioctl(fd, VIDIOC_QUERYCAP, &cap) >= 0)
                        {
                            if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE)
                            {
                                // 获取设备名称
                                QString deviceName = QString((char *)cap.card);

                                // 读取设备的VID和PID
                                QString ueventPath = QString("/sys/class/video4linux/%1/device/uevent").arg(ent->d_name);
                                QFile ueventFile(ueventPath);
                                QString vid, pid;
                                if (ueventFile.open(QIODevice::ReadOnly | QIODevice::Text))
                                {
                                    QTextStream in(&ueventFile);
                                    while (!in.atEnd())
                                    {
                                        QString line = in.readLine();
                                        if (line.startsWith("MODALIAS="))
                                        {
                                            QRegExp regex("usb:v([0-9A-Fa-f]{4})p([0-9A-Fa-f]{4})");
                                            if (regex.indexIn(line) != -1)
                                            {
                                                vid = regex.cap(1).toLower();
                                                pid = regex.cap(2).toLower();
                                            }
                                            break;
                                        }
                                    }
                                    ueventFile.close();
                                }
                                // if (pid == "32e4")
                                {
                                    // 简化设备名称，只保留冒号前面的部分
                                    QString simplifiedName = deviceName.section(':', 0, 0);
                                    tempDevices.append(qMakePair(deviceNumber, simplifiedName));
                                }
                            }
                        }
                        ::close(fd);
                    }
                }
            }
        }
        closedir(dir);

        // 按设备号排序
        std::sort(tempDevices.begin(), tempDevices.end(), [](const QPair<int, QString> &a, const QPair<int, QString> &b)
                  {
                      return a.first < b.first; // 按设备号升序
                  });

        // 转换为最终格式
        for (const auto &device : tempDevices)
        {
            devices.append(qMakePair(device.second, "/dev/video" + QString::number(device.first)));
        }
    }
    else
    {

    }

    return devices;
}

// 查找视频格式
QList<QPair<QString, QVariantMap>> CameraParams::findVideoFormats(const QString &devicePath)
{
    QList<QPair<QString, QVariantMap>> formats; // <格式名称, 格式数据>

    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {

        return formats;
    }

    // 打开设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {

        return formats;
    }

    // 查询设备支持的格式
    struct v4l2_fmtdesc fmtdesc;
    memset(&fmtdesc, 0, sizeof(fmtdesc));
    fmtdesc.type = V4L2_BUF_TYPE_VIDEO_CAPTURE;

    // 用于存储已添加的格式，避免重复
    QSet<QString> addedFormats;

    // 遍历所有支持的像素格式
    while (ioctl(fd, VIDIOC_ENUM_FMT, &fmtdesc) == 0)
    {
        // 获取像素格式的名称
        QString pixelFormatName;
        char formatStr[5] = {0};
        formatStr[0] = (fmtdesc.pixelformat & 0xFF);
        formatStr[1] = (fmtdesc.pixelformat >> 8) & 0xFF;
        formatStr[2] = (fmtdesc.pixelformat >> 16) & 0xFF;
        formatStr[3] = (fmtdesc.pixelformat >> 24) & 0xFF;
        pixelFormatName = QString(formatStr);

        // 检查是否是H264格式，跳过不添加
        if (pixelFormatName.toUpper() == "H264") {
            fmtdesc.index++;
            continue;
        }

        // 创建用于存储格式数据的Map
        QVariantMap formatData;
        formatData["pixelformat"] = fmtdesc.pixelformat;

        // 检查是否已添加过该格式
        if (!addedFormats.contains(pixelFormatName))
        {
            formats.append(qMakePair(pixelFormatName, formatData));
            addedFormats.insert(pixelFormatName);
        }

        fmtdesc.index++;
    }

    // 关闭设备
    ::close(fd);

    return formats;
}

// 查找视频分辨率和帧率
QList<QPair<QString, QVariantMap>> CameraParams::findVideoResolutions(const QString &devicePath, uint32_t pixelformat)
{
    QList<QPair<QString, QVariantMap>> resolutions; // <分辨率描述, 分辨率数据>

    // 检查设备路径是否为空
    if (devicePath.isEmpty())
    {

        return resolutions;
    }

    // 打开设备
    int fd = open(devicePath.toUtf8().constData(), O_RDWR);
    if (fd < 0)
    {

        return resolutions;
    }

    // 用于存储已添加的分辨率和帧率组合，避免重复
    QSet<QString> addedResolutionFramerates;

    // 查询该像素格式支持的分辨率
    struct v4l2_frmsizeenum frmsize;
    memset(&frmsize, 0, sizeof(frmsize));
    frmsize.pixel_format = pixelformat;
    frmsize.index = 0;

    // 遍历该像素格式支持的所有分辨率
    while (ioctl(fd, VIDIOC_ENUM_FRAMESIZES, &frmsize) == 0)
    {
        if (frmsize.type == V4L2_FRMSIZE_TYPE_DISCRETE)
        {
            // 对每个分辨率，查询支持的帧率
            struct v4l2_frmivalenum frmival;
            memset(&frmival, 0, sizeof(frmival));
            frmival.pixel_format = pixelformat;
            frmival.width = frmsize.discrete.width;
            frmival.height = frmsize.discrete.height;
            frmival.index = 0;

            // 检查分辨率是否为我们需要的（4K、1080P、720P）
            bool isTargetResolution = false;
            if ((frmsize.discrete.width == 3840 && frmsize.discrete.height == 2160) ||  // 4K
                (frmsize.discrete.width == 1920 && frmsize.discrete.height == 1080) ||  // 1080P
                (frmsize.discrete.width == 1280 && frmsize.discrete.height == 720))     // 720P
            {
                isTargetResolution = true;
            }

            if (isTargetResolution)
            {
                // 遍历该分辨率支持的所有帧率
                while (ioctl(fd, VIDIOC_ENUM_FRAMEINTERVALS, &frmival) == 0)
                {
                    if (frmival.type == V4L2_FRMIVAL_TYPE_DISCRETE)
                    {
                        // 计算帧率（分数形式：分子/分母）
                        float framerate = (float)frmival.discrete.denominator / frmival.discrete.numerator;

                        // 只保存60、30、15帧率
                        bool isTargetFramerate = false;
                        if (qAbs(framerate - 60.0) < 0.1 ||  // 60fps
                            qAbs(framerate - 30.0) < 0.1 ||  // 30fps
                            qAbs(framerate - 15.0) < 0.1)    // 15fps
                        {
                            isTargetFramerate = true;
                        }

                        if (isTargetFramerate)
                        {
                            // 格式化显示字符串
                            QString displayStr = QString("%1x%2 @%3fps")
                                                     .arg(frmsize.discrete.width)
                                                     .arg(frmsize.discrete.height)
                                                     .arg(framerate, 0, 'f', 1);

                            // 创建用于存储分辨率数据的Map
                            QVariantMap resolutionData;
                            resolutionData["width"] = frmsize.discrete.width;
                            resolutionData["height"] = frmsize.discrete.height;
                            resolutionData["framerate_num"] = frmival.discrete.denominator;
                            resolutionData["framerate_den"] = frmival.discrete.numerator;

                            // 检查是否已添加过该分辨率和帧率组合
                            if (!addedResolutionFramerates.contains(displayStr))
                            {
                                resolutions.append(qMakePair(displayStr, resolutionData));
                                addedResolutionFramerates.insert(displayStr);
                            }
                        }
                    }
                    frmival.index++;
                }
            }
        }
        frmsize.index++;
    }

    // 关闭设备
    ::close(fd);

    return resolutions;
}

// 选择首选格式和分辨率
void CameraParams::selectPreferredFormat(const QList<QPair<QString, QVariantMap>> &formats,
                                         const QList<QPair<QString, QVariantMap>> &resolutions,
                                         int &formatIndex, int &resolutionIndex)
{
    formatIndex = -1;
    resolutionIndex = -1;

    // 查找并选择MJPEG格式
    for (int i = 0; i < formats.size(); i++) {
        QString formatName = formats[i].first;
        if (formatName.toUpper() == "MJPG" || formatName.toUpper() == "JPEG") {
            formatIndex = i;
            break;
        }
    }

    // 如果没有找到MJPEG格式且有其他格式可用，则选择第一个
    if (formatIndex == -1 && !formats.isEmpty()) {
        formatIndex = 0;
    }

    // 尝试查找1080p分辨率且帧率接近30fps的选项
    float bestFpsMatch = 100.0f; // 初始值设置为很大，用于寻找最接近30fps的帧率

    for (int i = 0; i < resolutions.size(); i++) {
        QVariantMap resData = resolutions[i].second;

        // 检查宽度和高度是否匹配1080p
        if (resData["width"].toInt() == 1920 && resData["height"].toInt() == 1080) {
            // 如果有帧率信息，尝试找到最接近30fps的选项
            if (resData.contains("framerate_num") && resData.contains("framerate_den")) {
                float fps = (float)resData["framerate_num"].toInt() / resData["framerate_den"].toInt();
                float fpsDiff = qAbs(fps - 30.0f);

                // 如果这个帧率比之前找到的更接近30fps
                if (fpsDiff < bestFpsMatch) {
                    bestFpsMatch = fpsDiff;
                    resolutionIndex = i;
                }
            } else if (resolutionIndex == -1) {
                // 如果还没找到任何匹配项且当前选项没有帧率信息，先记录下来
                resolutionIndex = i;
            }
        }
    }

    // 如果没有找到1080p的分辨率，则尝试找任何分辨率
    if (resolutionIndex == -1 && !resolutions.isEmpty()) {
        resolutionIndex = 0; // 默认使用第一个
    }

}

// 根据USB接口查找对应的音频设备
QList<QPair<QString, QString>> CameraParams::findAudioDevicesForCamera(const QString &usbInterface)
{
    QList<QPair<QString, QString>> audioDevices; // <设备名称, 设备路径>

    if (usbInterface.isEmpty()) {
        return audioDevices;
    }

    // 根据用户提供的规则，将USB接口的最后一位数字改为2来查找音频设备
    // 例如：5-1.2:1.0 -> 5-1.2:1.2
    QString audioInterface = usbInterface;
    QRegularExpression regex("(\\d+-\\d+(?:\\.\\d+)*:\\d+\\.)\\d+$");
    QRegularExpressionMatch match = regex.match(usbInterface);

    if (match.hasMatch()) {
        QString prefix = match.captured(1);
        audioInterface = prefix + "2";  // 将最后一位改为2

        // 检查音频接口目录是否存在
        QString audioInterfacePath = QString("/sys/bus/usb/devices/%1").arg(audioInterface);
        QDir audioDir(audioInterfacePath);

        if (audioDir.exists()) {
            // 查找sound目录
            QString soundPath = audioInterfacePath + "/sound";
            QDir soundDir(soundPath);

            if (soundDir.exists()) {
                // 遍历sound目录下的所有条目
                QStringList soundEntries = soundDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

                for (const QString &entry : soundEntries) {
                    // 检查是否是cardX格式的音频卡
                    QRegularExpression cardRegex("^card(\\d+)$");
                    QRegularExpressionMatch cardMatch = cardRegex.match(entry);

                    if (cardMatch.hasMatch()) {
                        QString cardNumber = cardMatch.captured(1);

                        // 读取音频卡的名称
                        QString cardNamePath = QString("/proc/asound/card%1/id").arg(cardNumber);
                        QFile cardNameFile(cardNamePath);
                        QString cardName = QString("Audio Card %1").arg(cardNumber);

                        if (cardNameFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
                            QTextStream in(&cardNameFile);
                            QString id = in.readLine().trimmed();
                            if (!id.isEmpty()) {
                                cardName = id;
                            }
                            cardNameFile.close();
                        }

                        // 构造ALSA设备路径
                        QString devicePath = QString("hw:%1").arg(cardNumber);
                        audioDevices.append(qMakePair(cardName, devicePath));

                        qDebug() << "找到摄像头音频设备:" << cardName << "路径:" << devicePath
                                 << "USB接口:" << audioInterface;
                    }
                }
            }
        }
    }

    return audioDevices;
}
