#include "camerastream.h"
#include <QDebug>
#include <QWidget>
#include "mainwindow.h"
#include <QGuiApplication>
#include <QScreen>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <QProcess>
#include <QRegExp>
#include <QImage>
#include <QThread>
#include <QFile>
#include "camera_param.h"

CameraStream::CameraStream(QObject *parent)
    : QObject{parent}
{
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    // 初始化录像相关变量
    isRecording = false;
    m_cached_record_branch = nullptr;
    m_record_tee_pad = nullptr;
    m_record_queue_pad = nullptr;

    // 初始化水印相关变量
    m_convert = nullptr;
    channelstream = 0;

    // 初始化拍照相关变量
    m_photoBranchCreated = false;
    m_photoRequested = false;
    m_photoPipelineActive = false;

    // 初始化缩略图拍照相关变量
    m_thumbPhotoRequested = false;

    // 获取MainWindow指针
    m_mainWindow = qobject_cast<MainWindow*>(parent);

    // 初始化摄像头监控
    m_cameraMonitorTimer = new QTimer(this);
    m_cameraConnected = false;
    m_monitoringEnabled = false;
    m_expectingEOS = false;
    connect(m_cameraMonitorTimer, &QTimer::timeout, this, &CameraStream::checkCameraStatus);
}

CameraStream::~CameraStream()
{
    // 清理录像分支
    if (m_cached_record_branch) {
        destroyRecordBranch();
    }

    // 停止摄像头监控
    stopCameraMonitoring();

    // 清理拍照分支
    if (m_photoBranchCreated) {
        GstElement* existing_sink = gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");
        if (existing_sink) {
            destroyPhotoSink(existing_sink);
            gst_object_unref(existing_sink);
        }
    }
}

bool CameraStream::start_camera()
{
    // 查找最佳摄像头设备
    QString bestDevice = findBestCameraDevice();
    if (bestDevice.isEmpty()) {
        qWarning() << "未找到可用的摄像头设备";
        return false;
    }

    qDebug() << "使用摄像头设备:" << bestDevice;

    // 获取设备支持的格式和分辨率（已按优先级排序）
    CameraParams cameraParams;
    QList<QPair<QString, QVariantMap>> formats = cameraParams.findVideoFormats(bestDevice);

    // 查找MJPEG格式
    uint32_t pixelformat = 0;
    for (const auto &format : formats) {
        QVariantMap formatData = format.second;
        if (formatData.contains("pixelformat")) {
            QString formatName = format.first;
            if (formatName.toUpper() == "MJPG" || formatName.toUpper() == "JPEG") {
                pixelformat = formatData["pixelformat"].toUInt();
                break;
            }
        }
    }

    // 默认分辨率和帧率
    int bestWidth = 1280, bestHeight = 720, bestFramerate = 30;

    if (pixelformat != 0) {
        // 获取支持的分辨率（已按优先级排序）
        QList<QPair<QString, QVariantMap>> resolutions = cameraParams.findVideoResolutions(bestDevice, pixelformat);

        if (!resolutions.isEmpty()) {
            // 使用第一个（最高优先级的）分辨率
            QVariantMap resData = resolutions.first().second;
            bestWidth = resData["width"].toInt();
            bestHeight = resData["height"].toInt();
            int framerateNum = resData["framerate_num"].toInt();
            int framerateDen = resData["framerate_den"].toInt();
            bestFramerate = framerateNum / framerateDen;
        }
    }

    qDebug() << "使用分辨率:" << bestWidth << "x" << bestHeight << "@" << bestFramerate << "fps";

    // 创建GStreamer管道元素
    pipeline = gst_pipeline_new("camera-pipeline");
    GstElement *source = gst_element_factory_make("v4l2src", "source");
    GstElement *filter = gst_element_factory_make("capsfilter", "filter");
    m_convert = gst_element_factory_make("videoconvert", "convert");
    // 添加tee元素，用于分流视频流 (保留tee以便后续动态添加拍照分支)
    tee = gst_element_factory_make("tee", "tee");
    // 主视频流分支（简化，去掉强制格式转换）
    GstElement *queue_main = gst_element_factory_make("queue", "queue_main");
    flip = gst_element_factory_make("videoflip", "flip");
    GstElement *preview_convert = gst_element_factory_make("videoconvert", "preview_convert");
    GstElement *sink = gst_element_factory_make("waylandsink", "preview_waylandsink");
    parser = gst_element_factory_make("identity", "parser");
    decoder = gst_element_factory_make("mppjpegdec", "decoder");
        if (!pipeline || !source || !filter || !parser || !decoder || !m_convert ||
        !tee || !flip || !preview_convert || !sink || !queue_main) {
        qWarning("unableToCreateGStreamerElement");
        return false;
    }
    // 配置视频源
    g_object_set(source,
                 "device", qPrintable(bestDevice),
                 "io-mode", 4, // DMA buffer mode
                 "queue-size", 2,      // 只能设 2~8，设最小
                 "drop-buffers", TRUE, // 上游满时丢帧
                 nullptr);
    g_object_set(decoder,
             "low-delay", TRUE,
             "max-frames", 1,      // 只留 1 帧
             nullptr);
    GstCaps *caps = nullptr;
    caps = gst_caps_new_simple("image/jpeg",
                                "width", G_TYPE_INT, bestWidth,
                                "height", G_TYPE_INT, bestHeight,
                                "framerate", GST_TYPE_FRACTION, bestFramerate, 1,
                                nullptr);
    g_object_set(filter, "caps", caps, nullptr);
    gst_caps_unref(caps);

    // 主屏幕
    QScreen *screen = QGuiApplication::primaryScreen();

    // 整个屏幕的完整几何（含任务栏、Dock、菜单栏等）
    QRect full = screen->geometry();
    qDebug() << "屏幕完整区域：" << full;

    // 屏幕的“可用”几何（去掉系统任务栏/Dock后的区域）
    QRect avail = screen->availableGeometry();
    qDebug() << "可用区域：" << avail;

    int x = avail.x();
    int y = avail.y();
    int w = avail.width();
    int h = avail.height();
    printf("x=%d,y=%d,w=%d,h=%d\n",x,y,w,h);
    // 设置渲染区域
    g_object_set(sink,
                 "sync", FALSE,  // 禁用同步（减少延迟）
                 "enable-last-sample", FALSE,  // 不保留最后一帧
                 "layer",2,
                 nullptr);

    g_object_set(flip, "video-direction", 0, nullptr);
    // 配置队列
    g_object_set(queue_main,
                 "max-size-buffers", 3,
                 "max-size-time", 0,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w, h};  // x, y, width, height
    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中RK
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }
    // 设置属性
    g_object_set_property(G_OBJECT(sink), "render-rectangle", &val);
    // 清理
    g_value_unset(&val);

    // 构建管道
    gst_bin_add_many(GST_BIN(pipeline),
                     source, filter, parser, decoder, m_convert, tee,
                     queue_main, flip, preview_convert, sink,
                     nullptr);
    // 初始化时不添加时间水印，通过动态插入/移除来控制
    // 默认链接：source -> filter -> parser -> decoder -> m_convert -> tee
    if (!gst_element_link_many(source, filter, parser, decoder, m_convert, tee, nullptr)) {
        qWarning("无法链接GStreamer元素到tee");
        return false;
    }
    // 链接主视频流分支：queue_main -> flip -> preview_convert -> sink
    if (!gst_element_link_many(queue_main, flip, preview_convert, sink, nullptr)) {
        qWarning("无法链接主视频流分支");
        return false;
    }

    // 链接tee到主视频流队列
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");

    // 主视频流pad
    GstPad *tee_main_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    GstPad *queue_main_pad = gst_element_get_static_pad(queue_main, "sink");

    // 链接pad
    if (gst_pad_link(tee_main_pad, queue_main_pad) != GST_PAD_LINK_OK) {
        gst_object_unref(tee_main_pad);
        gst_object_unref(queue_main_pad);
        qWarning("无法链接tee pad到主视频流");
        return false;
    }
    // 解引用不再需要的pad
    gst_object_unref(queue_main_pad);
    gst_object_unref(tee_main_pad);

    // 设置总线消息监听
    GstBus *bus = gst_pipeline_get_bus(GST_PIPELINE(pipeline));
    gst_bus_add_watch(bus, busCallback, this);
    gst_object_unref(bus);

    // 启动管道
    GstStateChangeReturn ret = gst_element_set_state(pipeline, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        qWarning("unableToStartPipeline");
        return false;
    }

    // 标记摄像头已连接并启动监控
    m_cameraConnected = true;
    startCameraMonitoring();

    return true;
}

// 创建录像分支
GstElement* CameraStream::createRecordBranch(const QString &filePath)
{
    if (!pipeline || !tee) {
        printf("无法创建录像分支：pipeline或tee未初始化\n");
        return nullptr;
    }

    printf("正在创建录像分支...\n");

    // 创建录像分支的元素
    GstElement *queue_rec = gst_element_factory_make("queue", "queue_rec");
    GstElement *encoder = gst_element_factory_make("mpph264enc", "encoder");
    GstElement *h264parse = gst_element_factory_make("h264parse", "h264parse");
    GstElement *splitmuxsink = gst_element_factory_make("splitmuxsink", "splitmuxsink");

    if (!queue_rec || !encoder || !h264parse || !splitmuxsink) {
        printf("无法创建录像分支的元素\n");
        if (queue_rec) gst_object_unref(queue_rec);
        if (encoder) gst_object_unref(encoder);
        if (h264parse) gst_object_unref(h264parse);
        if (splitmuxsink) gst_object_unref(splitmuxsink);
        return nullptr;
    }

    // 配置队列
    g_object_set(queue_rec,
                 "max-size-buffers", 60,
                 "max-size-time", 1 * GST_SECOND,
                 "max-size-bytes", 0,
                 "leaky", 2,
                 "flush-on-eos", TRUE,    // EOS时刷新队列
                 NULL);

    // 配置编码器
    g_object_set(encoder,
                 "bps", 40000000,  // 40Mbps码率
                 "gop", 30,
                 "profile", 66,   // Baseline profile
                 "rc-mode", 1,    // CBR模式
                 NULL);

    // 配置splitmuxsink
    g_object_set(splitmuxsink,
                 "location", filePath.toUtf8().constData(),
                 "muxer", gst_element_factory_make("mp4mux", "muxer"),
                 NULL);

    // 将元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);

    // 链接元素：queue_rec -> encoder -> h264parse -> splitmuxsink
    bool linkSuccess = gst_element_link_many(queue_rec, encoder, h264parse, splitmuxsink, NULL);
    if (!linkSuccess) {
        printf("无法链接录像分支元素\n");
        gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, splitmuxsink, NULL);
        return nullptr;
    }

    // 从tee获取pad
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    m_record_tee_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    m_record_queue_pad = gst_element_get_static_pad(queue_rec, "sink");

    // 存储引用到splitmuxsink的私有数据中，以便后续清理
    g_object_set_data(G_OBJECT(splitmuxsink), "queue", queue_rec);
    g_object_set_data(G_OBJECT(splitmuxsink), "encoder", encoder);
    g_object_set_data(G_OBJECT(splitmuxsink), "h264parse", h264parse);

    // 同步元素状态
    gst_element_sync_state_with_parent(queue_rec);
    gst_element_sync_state_with_parent(encoder);
    gst_element_sync_state_with_parent(h264parse);
    gst_element_sync_state_with_parent(splitmuxsink);

    printf("录像分支创建成功\n");
    return splitmuxsink;
}

// 销毁录像分支
void CameraStream::destroyRecordBranch()
{
    if (!m_cached_record_branch) {
        printf("录像分支已为空，无需销毁\n");
        return;
    }

    if (!pipeline) {
        printf("管道已为空，直接清理录像分支引用\n");
        m_cached_record_branch = nullptr;
        m_record_tee_pad = nullptr;
        m_record_queue_pad = nullptr;
        return;
    }

    printf("正在销毁录像分支...\n");

    // 先停用录像分支
    deactivateRecordBranch();

    // 获取之前存储的元素引用
    GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
    GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
    GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

    if (queue_rec && encoder && h264parse) {
        // 将元素状态设置为NULL
        printf("设置录像分支元素状态为NULL...\n");
        gst_element_set_state(m_cached_record_branch, GST_STATE_NULL);
        gst_element_set_state(h264parse, GST_STATE_NULL);
        gst_element_set_state(encoder, GST_STATE_NULL);
        gst_element_set_state(queue_rec, GST_STATE_NULL);

        // 等待状态变化完成
        gst_element_get_state(m_cached_record_branch, NULL, NULL, 1 * GST_SECOND);
    }

    // 安全地释放pad引用
    if (m_record_tee_pad && tee) {
        printf("释放tee的请求pad...\n");
        gst_element_release_request_pad(tee, m_record_tee_pad);
        gst_object_unref(m_record_tee_pad);
        m_record_tee_pad = nullptr;
    }

    if (m_record_queue_pad) {
        gst_object_unref(m_record_queue_pad);
        m_record_queue_pad = nullptr;
    }

    // 从管道中移除元素
    if (queue_rec && encoder && h264parse) {
        printf("从管道中移除录像分支元素...\n");
        gst_bin_remove_many(GST_BIN(pipeline), queue_rec, encoder, h264parse, m_cached_record_branch, NULL);
    } else {
        printf("部分元素引用丢失，尝试单独移除splitmuxsink...\n");
        gst_bin_remove(GST_BIN(pipeline), m_cached_record_branch);
    }

    m_cached_record_branch = nullptr;
    printf("录像分支已销毁\n");
}

// 激活录像分支（链接tee_pad和queue_pad）
bool CameraStream::activateRecordBranch()
{
    printf("开始激活录像分支...\n");

    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("无法激活录像分支：pad未初始化 (tee_pad=%p, queue_pad=%p)\n",
               m_record_tee_pad, m_record_queue_pad);
        return false;
    }

    if (!tee || !m_cached_record_branch) {
        printf("无法激活录像分支：tee或录像分支未初始化 (tee=%p, record_branch=%p)\n",
               tee, m_cached_record_branch);
        return false;
    }

    // 检查pad是否已经连接
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("录像分支pad已连接，无需重复激活\n");
        isRecording = true;
        return true;
    }

    printf("准备链接tee pad到录像分支...\n");

    // 激活pad
    gst_pad_set_active(m_record_tee_pad, TRUE);

    // 链接pad
    GstPadLinkReturn link_result = gst_pad_link(m_record_tee_pad, m_record_queue_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接tee到录像分支，错误代码: %d\n", link_result);
        return false;
    }

    printf("录像分支pad连接成功\n");
    printf("录像分支已激活\n");
    isRecording = true;

    return true;
}

// 停用录像分支（断开tee_pad和queue_pad）
bool CameraStream::deactivateRecordBranch()
{
    if (!m_record_tee_pad || !m_record_queue_pad) {
        printf("录像分支pad未初始化或已释放\n");
        isRecording = false;
        return true;
    }

    // 检查连接状态并断开
    if (gst_pad_is_linked(m_record_tee_pad)) {
        printf("断开录像分支pad连接...\n");
        if (gst_pad_unlink(m_record_tee_pad, m_record_queue_pad)) {
            printf("录像分支pad连接已断开\n");
        } else {
            printf("警告：断开录像分支pad连接失败\n");
        }
    } else {
        printf("录像分支pad已断开或未连接\n");
    }

    printf("录像分支已停用\n");
    isRecording = false;

    return true;
}

// 开始录像
void CameraStream::startRecording()
{
    // 防止重复调用
    if (isRecording) {
        printf("录像已在进行中，忽略重复调用\n");
        return;
    }

    printf("开始录像...\n");

    // 检查摄像头连接状态
    if (!m_cameraConnected) {
        printf("摄像头未连接，无法开始录像\n");
        return;
    }

    // 确保摄像头已经初始化
    if (!pipeline || !tee) {
        printf("摄像头未初始化，无法开始录像\n");
        return;
    }

    // 生成录像文件路径
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss-zzz");
    QString fileName = QString("%1.mp4").arg(timestamp);

    // 创建录像目录
    QString recordingDir = "/data/recordings";
    QDir dir;
    if (!dir.exists(recordingDir)) {
        dir.mkpath(recordingDir);
    }

    QString filePath = recordingDir + "/" + fileName;
    recordingfilename = filePath;

    printf("录像文件路径: %s\n", qPrintable(filePath));

    // 检查是否已经有录像分支
    if (m_cached_record_branch) {
        destroyRecordBranch();
    }

    // 检查是否需要录制音频
    bool enableAudio = false;
    if (m_mainWindow) {
        enableAudio = m_mainWindow->curaudiostate;
    }

    // 先创建音频分支（如果需要），但不激活
    bool audioSuccess = false;
    if (enableAudio) {
        printf("尝试创建音频分支，使用设备: hw:2,0\n");
        if (createAudioBranch("hw:2,0")) {
            printf("音频分支创建成功\n");
            audioSuccess = true;
        } else {
            printf("无法创建音频分支，继续进行视频录制\n");
        }
    } else {
        printf("音频录制未启用\n");
    }

    // 创建新的录像分支
    m_cached_record_branch = createRecordBranch(filePath);
    if (!m_cached_record_branch) {
        printf("无法创建录像分支\n");
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 激活录像分支
    if (!activateRecordBranch()) {
        printf("无法激活录像分支\n");
        destroyRecordBranch();
        if (audioSuccess) {
            destroyAudioBranch();
        }
        return;
    }

    // 激活音频分支（如果已创建）
    if (audioSuccess) {
        printf("尝试激活音频分支...\n");
        if (activateAudioBranch()) {
            printf("音频分支已成功激活\n");
        } else {
            printf("无法激活音频分支，继续进行视频录制\n");
            destroyAudioBranch();
            audioSuccess = false;
        }
    }

    if (enableAudio && !audioSuccess) {
        printf("音频录制失败，但视频录制将继续\n");
    }

    // 禁用相机控制，防止录像时修改设置
    if (m_mainWindow) {
        m_mainWindow->disableCameraControls();
    }

    printf("录像已开始，文件: %s\n", qPrintable(filePath));

    // 录像开始后稍微延迟再拍摄缩略图，让管道状态稳定
    QTimer::singleShot(500, [this, fileName]() {
        takeThumbPhoto(fileName);
    });
}

// 停止录像
void CameraStream::stopRecording()
{
    // 防止重复调用
    if (!isRecording) {
        printf("录像未在进行中，忽略停止调用\n");
        return;
    }

    printf("停止录像...\n");

    // 设置期待EOS标志，避免触发异常恢复
    m_expectingEOS = true;

    // 发送EOS信号确保录像文件正确关闭
    if (m_cached_record_branch) {
        printf("正在结束录像，确保正确关闭文件...\n");

        // 获取录像分支的所有元素
        GstElement *queue_rec = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "queue");
        GstElement *encoder = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "encoder");
        GstElement *h264parse = (GstElement*)g_object_get_data(G_OBJECT(m_cached_record_branch), "h264parse");

        if (queue_rec && encoder && h264parse) {
            // 设置probe阻止新数据通过
            if (m_record_tee_pad && gst_pad_is_linked(m_record_tee_pad)) {
                printf("阻止新数据进入录像分支...\n");
                gulong probe_id = gst_pad_add_probe(m_record_tee_pad,
                                                    GST_PAD_PROBE_TYPE_BLOCK_DOWNSTREAM,
                                                    (GstPadProbeCallback)NULL, NULL, NULL);

                // 让已有数据有时间流入分支
                g_usleep(100000); // 等待100ms

                // 发送EOS信号到视频和音频分支
                bool video_eos_sent = false;
                bool audio_eos_sent = false;

                // 向音频分支发送EOS（如果存在）
                if (m_audio_source) {
                    printf("向音频分支发送EOS...\n");
                    if (gst_element_send_event(m_audio_source, gst_event_new_eos())) {
                        printf("向音频源发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_queue && gst_element_send_event(m_audio_queue, gst_event_new_eos())) {
                        printf("向音频队列发送EOS成功\n");
                        audio_eos_sent = true;
                    } else if (m_audio_convert && gst_element_send_event(m_audio_convert, gst_event_new_eos())) {
                        printf("向音频转换器发送EOS成功\n");
                        audio_eos_sent = true;
                    } else {
                        printf("向音频分支发送EOS失败\n");
                    }
                }

                // 向视频分支发送EOS
                if (gst_element_send_event(queue_rec, gst_event_new_eos())) {
                    printf("向queue_rec发送EOS成功\n");
                    video_eos_sent = true;
                } else {
                    printf("向queue_rec发送EOS失败，尝试其他元素\n");

                    // 如果失败，尝试向encoder发送
                    if (gst_element_send_event(encoder, gst_event_new_eos())) {
                        printf("向encoder发送EOS成功\n");
                        video_eos_sent = true;
                    } else {
                        printf("向encoder发送EOS失败，尝试h264parse\n");

                        // 再尝试向h264parse发送
                        if (gst_element_send_event(h264parse, gst_event_new_eos())) {
                            printf("向h264parse发送EOS成功\n");
                            video_eos_sent = true;
                        } else {
                            printf("向h264parse发送EOS失败，尝试muxsink\n");

                            // 最后尝试向splitmuxsink发送
                            if (gst_element_send_event(m_cached_record_branch, gst_event_new_eos())) {
                                printf("向splitmuxsink发送EOS成功\n");
                                video_eos_sent = true;
                            } else {
                                printf("所有视频EOS发送尝试均失败\n");
                            }
                        }
                    }
                }

                // 检查EOS发送结果
                bool eos_sent = video_eos_sent || audio_eos_sent;
                printf("EOS发送结果: 视频=%s, 音频=%s\n",
                       video_eos_sent ? "成功" : "失败",
                       audio_eos_sent ? "成功" : "失败");

                if (eos_sent) {
                    // 等待EOS信号处理完成
                    printf("等待EOS处理完成...\n");

                    // 等待状态变化，确保元素开始处理EOS
                    gst_element_get_state(m_cached_record_branch, NULL, NULL, 300 * GST_MSECOND);

                    // 等待EOS或错误消息
                    GstBus* bus = gst_element_get_bus(pipeline);
                    GstMessage* msg = gst_bus_timed_pop_filtered(bus, 100 * GST_MSECOND,
                                                                 (GstMessageType)(GST_MESSAGE_EOS | GST_MESSAGE_ERROR));

                    if (msg) {
                        switch (GST_MESSAGE_TYPE(msg)) {
                        case GST_MESSAGE_EOS:
                            printf("EOS处理完成，录像已正常结束\n");
                            break;
                        case GST_MESSAGE_ERROR:
                            printf("处理EOS时发生错误，但继续清理\n");
                            break;
                        default:
                            break;
                        }
                        gst_message_unref(msg);
                    } else {
                        printf("等待EOS超时，但可能已成功处理\n");
                    }

                    gst_object_unref(bus);

                    // 给一些时间让所有数据写入完成
                    printf("等待所有数据写入完成...\n");
                    g_usleep(100000); // 等待300ms
                }

                // 移除probe
                if (m_record_tee_pad) {
                    gst_pad_remove_probe(m_record_tee_pad, probe_id);
                }
            }
        }

        // 销毁录像分支
        destroyRecordBranch();
    }

    // 清理音频分支（如果存在）
    if (m_audio_source || m_audio_queue || m_audio_convert || m_audio_encoder || m_audio_parser) {
        printf("清理音频分支\n");
        destroyAudioBranch();
    }

    // 暂停拍照分支（如果存在且激活）
    if (m_photoPipelineActive) {
        printf("暂停拍照分支\n");
        deactivatePhotoPipeline();
    }

    // 重新启用相机控制
    if (m_mainWindow) {
        m_mainWindow->enableCameraControls();
    }

    // 重置EOS期待标志
    m_expectingEOS = false;

    printf("录像已停止\n");
}









// 创建音频分支
bool CameraStream::createAudioBranch(const QString &audioDevice)
{
    if (!pipeline) {
        printf("无法创建音频分支：pipeline未初始化\n");
        return false;
    }

    if (audioDevice.isEmpty()) {
        printf("音频设备路径为空，跳过音频分支创建\n");
        return true; // 不是错误，只是没有音频
    }

    printf("正在创建音频分支，设备: %s\n", qPrintable(audioDevice));

    // 创建音频元素
    m_audio_source = gst_element_factory_make("alsasrc", "audio_source");
    m_audio_queue = gst_element_factory_make("queue", "audio_queue");
    m_audio_convert = gst_element_factory_make("audioconvert", "audio_convert");
    m_audio_encoder = gst_element_factory_make("fdkaac", "audio_encoder");
    m_audio_parser = gst_element_factory_make("aacparse", "audio_parser");

    if (!m_audio_source || !m_audio_queue || !m_audio_convert || !m_audio_encoder || !m_audio_parser) {
        printf("无法创建音频分支的元素\n");
        destroyAudioBranch();
        return false;
    }

    // 配置音频源 - 固定使用 hw:2,0
    g_object_set(m_audio_source,
                 "device", "hw:2,0",
                 NULL);

    // 配置音频队列
    g_object_set(m_audio_queue,
                 "max-size-buffers", 200,
                 "max-size-time", 2 * GST_SECOND,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 配置音频编码器
    g_object_set(m_audio_encoder,
                 "bitrate", 128000, // 128kbps
                 NULL);

    // 将音频元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), m_audio_source, m_audio_queue,
                     m_audio_convert, m_audio_encoder, m_audio_parser, NULL);

    // 简化音频链接，让GStreamer自动协商格式
    if (!gst_element_link_many(m_audio_source, m_audio_queue, m_audio_convert,
                               m_audio_encoder, m_audio_parser, NULL)) {
        printf("无法链接音频分支元素\n");
        destroyAudioBranch();
        return false;
    }

    // 同步元素状态
    gst_element_sync_state_with_parent(m_audio_source);
    gst_element_sync_state_with_parent(m_audio_queue);
    gst_element_sync_state_with_parent(m_audio_convert);
    gst_element_sync_state_with_parent(m_audio_encoder);
    gst_element_sync_state_with_parent(m_audio_parser);

    m_audio_device_path = audioDevice;
    printf("音频分支创建成功\n");

    return true;
}

// 销毁音频分支
void CameraStream::destroyAudioBranch()
{
    if (!m_audio_source && !m_audio_queue && !m_audio_convert && !m_audio_encoder && !m_audio_parser) {
        return; // 没有音频分支需要销毁
    }

    printf("正在销毁音频分支...\n");

    // 停止音频元素
    if (m_audio_source) {
        gst_element_set_state(m_audio_source, GST_STATE_NULL);
    }
    if (m_audio_queue) {
        gst_element_set_state(m_audio_queue, GST_STATE_NULL);
    }
    if (m_audio_convert) {
        gst_element_set_state(m_audio_convert, GST_STATE_NULL);
    }
    if (m_audio_encoder) {
        gst_element_set_state(m_audio_encoder, GST_STATE_NULL);
    }
    if (m_audio_parser) {
        gst_element_set_state(m_audio_parser, GST_STATE_NULL);
    }

    // 从管道中移除音频元素
    if (pipeline) {
        if (m_audio_source) gst_bin_remove(GST_BIN(pipeline), m_audio_source);
        if (m_audio_queue) gst_bin_remove(GST_BIN(pipeline), m_audio_queue);
        if (m_audio_convert) gst_bin_remove(GST_BIN(pipeline), m_audio_convert);
        if (m_audio_encoder) gst_bin_remove(GST_BIN(pipeline), m_audio_encoder);
        if (m_audio_parser) gst_bin_remove(GST_BIN(pipeline), m_audio_parser);
    }

    // 清理引用
    m_audio_source = nullptr;
    m_audio_queue = nullptr;
    m_audio_convert = nullptr;
    m_audio_encoder = nullptr;
    m_audio_parser = nullptr;
    m_audio_device_path.clear();

    printf("音频分支已销毁\n");
}

// 激活音频分支（连接到splitmuxsink）
bool CameraStream::activateAudioBranch()
{
    if (!m_audio_parser || !m_cached_record_branch) {
        printf("无法激活音频分支：音频parser或录像分支未初始化\n");
        return false;
    }

    // 等待音频元素准备就绪
    gst_element_get_state(m_audio_parser, NULL, NULL, 1 * GST_SECOND);

    // 获取音频parser的src pad
    GstPad *audio_src_pad = gst_element_get_static_pad(m_audio_parser, "src");
    if (!audio_src_pad) {
        printf("无法获取音频parser的src pad\n");
        return false;
    }

    // 获取splitmuxsink的audio sink pad
    GstPad *mux_audio_pad = gst_element_get_request_pad(m_cached_record_branch, "audio_%u");
    if (!mux_audio_pad) {
        printf("无法获取splitmuxsink的audio pad\n");
        gst_object_unref(audio_src_pad);
        return false;
    }

    // 链接音频到muxer
    GstPadLinkReturn link_result = gst_pad_link(audio_src_pad, mux_audio_pad);
    if (link_result != GST_PAD_LINK_OK) {
        printf("无法链接音频到muxer，错误代码: %d\n", link_result);

        // 打印更详细的错误信息
        switch (link_result) {
        case GST_PAD_LINK_WRONG_HIERARCHY:
            printf("错误：pad不在同一管道中\n");
            break;
        case GST_PAD_LINK_WAS_LINKED:
            printf("错误：pad已经连接\n");
            break;
        case GST_PAD_LINK_WRONG_DIRECTION:
            printf("错误：pad方向错误\n");
            break;
        case GST_PAD_LINK_NOFORMAT:
            printf("错误：pad格式不兼容\n");
            break;
        case GST_PAD_LINK_NOSCHED:
            printf("错误：调度器问题\n");
            break;
        case GST_PAD_LINK_REFUSED:
            printf("错误：连接被拒绝\n");
            break;
        default:
            printf("错误：未知连接错误\n");
            break;
        }

        gst_object_unref(audio_src_pad);
        gst_object_unref(mux_audio_pad);
        return false;
    }

    printf("音频分支已成功连接到splitmuxsink\n");

    // 清理pad引用
    gst_object_unref(audio_src_pad);
    gst_object_unref(mux_audio_pad);

    return true;
}

// 创建拍照用的分支和appsink
GstElement* CameraStream::createPhotoSink()
{
    if (!pipeline || !tee) {
        printf("createPhotoSink失败: pipeline或tee未初始化 (pipeline=%p, tee=%p)\n", pipeline, tee);
        return nullptr;
    }

    if (m_photoBranchCreated) {
        GstElement* existing_sink = gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");
        if (existing_sink) {
            printf("使用已存在的拍照分支\n");
            return existing_sink;
        } else {
            printf("拍照分支标记为已创建但元素不存在，重新创建\n");
            m_photoBranchCreated = false;
        }
    }

    GstElement* valve = gst_element_factory_make("valve", "photo_valve");
    GstElement* queue = gst_element_factory_make("queue", "queue_photo");
    GstElement* encoder = gst_element_factory_make("mppjpegenc", "photo_encoder");
    GstElement* appsink = gst_element_factory_make("appsink", "photo_sink");

    if (!valve || !queue || !encoder || !appsink) {
        printf("拍照元素创建失败: valve=%p, queue=%p, encoder=%p, appsink=%p\n",
               valve, queue, encoder, appsink);
        qWarning() << "拍照元素创建失败，可能是GStreamer插件缺失";

        // 清理已创建的元素
        if (valve) gst_object_unref(valve);
        if (queue) gst_object_unref(queue);
        if (encoder) gst_object_unref(encoder);
        if (appsink) gst_object_unref(appsink);
        return nullptr;
    }

    // 配置队列
    g_object_set(queue,
                 "max-size-buffers", 2,
                 "max-size-time", 0,
                 "max-size-bytes", 0,
                 "leaky", 2, // 下游丢帧
                 NULL);

    // 配置JPEG编码器
    g_object_set(encoder,
                 "quality", 85,  // JPEG质量
                 NULL);

    // 配置appsink - 接收JPEG数据
    GstCaps* caps = gst_caps_new_simple("image/jpeg",
                                        NULL);
    g_object_set(appsink,
                 "emit-signals", TRUE,   // 启用信号
                 "sync", FALSE,
                 "drop", TRUE,
                 "max-buffers", 1,
                 "caps", caps,
                 NULL);
    gst_caps_unref(caps);

    // 连接new-sample信号到回调函数
    g_signal_connect(appsink, "new-sample", G_CALLBACK(photoSampleCallback), this);

    // 配置valve（默认关闭，需要时打开）
    g_object_set(valve, "drop", TRUE, NULL);

    // 将元素添加到管道
    gst_bin_add_many(GST_BIN(pipeline), valve, queue, encoder, appsink, NULL);

    // 链接元素：valve -> queue -> encoder -> appsink
    if (!gst_element_link_many(valve, queue, encoder, appsink, NULL)) {
        printf("无法链接拍照分支元素\n");
        gst_bin_remove_many(GST_BIN(pipeline), valve, queue, encoder, appsink, NULL);
        return nullptr;
    }

    // 从tee获取pad并连接到valve
    GstPadTemplate *tee_src_pad_template = gst_element_class_get_pad_template(GST_ELEMENT_GET_CLASS(tee), "src_%u");
    GstPad *tee_photo_pad = gst_element_request_pad(tee, tee_src_pad_template, NULL, NULL);
    GstPad *valve_sink_pad = gst_element_get_static_pad(valve, "sink");

    if (gst_pad_link(tee_photo_pad, valve_sink_pad) != GST_PAD_LINK_OK) {
        printf("无法链接tee到拍照分支\n");
        gst_object_unref(tee_photo_pad);
        gst_object_unref(valve_sink_pad);
        gst_bin_remove_many(GST_BIN(pipeline), valve, queue, encoder, appsink, NULL);
        return nullptr;
    }

    // 存储引用到appsink的私有数据中，以便后续清理
    g_object_set_data(G_OBJECT(appsink), "tee-pad", tee_photo_pad);
    g_object_set_data(G_OBJECT(appsink), "valve", valve);
    g_object_set_data(G_OBJECT(appsink), "queue", queue);
    g_object_set_data(G_OBJECT(appsink), "encoder", encoder);

    gst_object_unref(valve_sink_pad);

    // 同步元素状态
    gst_element_sync_state_with_parent(valve);
    gst_element_sync_state_with_parent(queue);
    gst_element_sync_state_with_parent(encoder);
    gst_element_sync_state_with_parent(appsink);

    m_photoBranchCreated = true;
    printf("拍照分支创建成功\n");
    return appsink;
}

// 销毁拍照分支
void CameraStream::destroyPhotoSink(GstElement* photo_sink)
{
    if (!pipeline || !photo_sink) {
        printf("destroyPhotoSink: pipeline或photo_sink为空 (pipeline=%p, photo_sink=%p)\n", pipeline, photo_sink);
        return;
    }

    printf("正在销毁拍照分支...\n");

    // 获取之前存储的pad和元素引用
    GstPad *tee_photo_pad = (GstPad*)g_object_get_data(G_OBJECT(photo_sink), "tee-pad");
    GstElement *valve_photo = (GstElement*)g_object_get_data(G_OBJECT(photo_sink), "valve");
    GstElement *queue_photo = (GstElement*)g_object_get_data(G_OBJECT(photo_sink), "queue");
    GstElement *encoder_photo = (GstElement*)g_object_get_data(G_OBJECT(photo_sink), "encoder");

    if (!tee_photo_pad || !queue_photo || !encoder_photo) {
        printf("无法获取拍照分支的引用，尝试通过名称查找\n");

        // 尝试通过名称查找元素
        valve_photo = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
        queue_photo = gst_bin_get_by_name(GST_BIN(pipeline), "queue_photo");
        encoder_photo = gst_bin_get_by_name(GST_BIN(pipeline), "photo_encoder");

        if (!valve_photo || !queue_photo || !encoder_photo) {
            printf("无法通过名称找到拍照分支元素，可能已被清理\n");
            m_photoBranchCreated = false;
            m_photoRequested = false;
            m_photoPipelineActive = false;
            return;
        }
    }

    // 设置元素状态为NULL
    gst_element_set_state(photo_sink, GST_STATE_NULL);
    gst_element_set_state(encoder_photo, GST_STATE_NULL);
    gst_element_set_state(queue_photo, GST_STATE_NULL);
    gst_element_set_state(valve_photo, GST_STATE_NULL);

    // 等待状态变化完成
    gst_element_get_state(photo_sink, NULL, NULL, 1 * GST_SECOND);

    // 释放tee的请求pad
    if (tee_photo_pad && tee) {
        gst_element_release_request_pad(tee, tee_photo_pad);
        gst_object_unref(tee_photo_pad);
    }

    // 从管道中移除元素
    gst_bin_remove_many(GST_BIN(pipeline), valve_photo, queue_photo, encoder_photo, photo_sink, NULL);

    // 清理引用
    if (valve_photo) gst_object_unref(valve_photo);
    if (queue_photo) gst_object_unref(queue_photo);
    if (encoder_photo) gst_object_unref(encoder_photo);

    // 重置拍照状态
    m_photoBranchCreated = false;
    m_photoRequested = false;
    m_photoPipelineActive = false;

    printf("拍照分支已销毁，状态已重置\n");
}



// 拍照回调函数 - 当appsink有新帧时自动调用
GstFlowReturn CameraStream::photoSampleCallback(GstElement *sink, gpointer data)
{
    CameraStream* stream = static_cast<CameraStream*>(data);
    if (!stream || (!stream->m_photoRequested && !stream->m_thumbPhotoRequested)) {
        return GST_FLOW_OK; // 如果没有请求拍照，直接返回
    }

    // 获取sample
    GstSample* sample = gst_app_sink_pull_sample(GST_APP_SINK(sink));
    if (!sample) {
        qWarning() << "无法获取拍照sample";
        return GST_FLOW_OK;
    }

    GstBuffer* buffer = gst_sample_get_buffer(sample);
    if (!buffer) {
        qWarning() << "拍照buffer为空";
        gst_sample_unref(sample);
        return GST_FLOW_OK;
    }

    // 映射buffer数据 - 这里是JPEG编码后的数据
    GstMapInfo map;
    if (gst_buffer_map(buffer, &map, GST_MAP_READ)) {
        // 获取MainWindow引用来获取保存路径
        if (stream->m_mainWindow) {
            QString basePath;
            QString filePath;

            // 判断是普通拍照还是缩略图拍照
            if (stream->m_thumbPhotoRequested) {
                // 缩略图拍照 - 保存到 /data/thumb 目录，128x128分辨率
                basePath = "/data/thumb";
                QDir dir(basePath);
                if (!dir.exists()) {
                    dir.mkpath(".");
                }
                filePath = basePath + "/" + stream->m_thumbPhotoFileName;

                // 需要将原始图像数据缩放到128x128
                // 由于我们在GStreamer管道中没有缩放，这里需要用Qt来处理
                QImage originalImage;
                if (originalImage.loadFromData(map.data, map.size, "JPEG")) {
                    // 缩放到128x128
                    QImage scaledImage = originalImage.scaled(128, 128, Qt::IgnoreAspectRatio, Qt::SmoothTransformation);

                    // 保存缩放后的图像
                    if (scaledImage.save(filePath, "JPEG", 85)) {
                        printf("缩略图拍照成功，保存到: %s (分辨率: 128x128)\n", qPrintable(filePath));
                        qDebug() << "Thumb Photo OK:" << filePath;
                    } else {
                        qWarning() << "无法保存缩略图文件:" << filePath;
                    }
                } else {
                    qWarning() << "无法解码JPEG数据进行缩略图处理";
                }
            } else {
                // 普通拍照 - 保存到 /data/photos 目录，原始分辨率
                basePath = "/data/photos";
                QDir dir(basePath);
                if (!dir.exists()) {
                    dir.mkpath(".");
                }
                filePath = basePath + "/" +
                           QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss-zzz") + ".jpg";

                // 直接保存JPEG数据到文件
                QFile file(filePath);
                if (file.open(QIODevice::WriteOnly)) {
                    qint64 bytesWritten = file.write(reinterpret_cast<const char*>(map.data), map.size);
                    file.close();

                    if (bytesWritten == map.size) {
                        printf("拍照成功，保存到: %s (大小: %zu bytes)\n", qPrintable(filePath), map.size);
                        qDebug() << "Photo OK:" << filePath;
                        // 发出拍照成功信号
                        emit stream->photoTaken();
                    } else {
                        qWarning() << "写入文件不完整:" << filePath << "期望:" << map.size << "实际:" << bytesWritten;
                    }
                } else {
                    qWarning() << "无法创建文件:" << filePath;
                }
            }
        } else {
            qWarning() << "无法获取MainWindow引用，拍照失败";
        }

        gst_buffer_unmap(buffer, &map);
    } else {
        qWarning() << "无法映射拍照buffer";
    }

    gst_sample_unref(sample);

    // 拍照完成，重置标志并停用管道
    if (stream->m_thumbPhotoRequested) {
        stream->m_thumbPhotoRequested = false;
    } else {
        stream->m_photoRequested = false;
    }
    stream->deactivatePhotoPipeline();

    return GST_FLOW_OK;
}

// 激活拍照管道
void CameraStream::activatePhotoPipeline()
{
    if (m_photoPipelineActive) {
        return; // 已经激活
    }

    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    if (valve) {
        g_object_set(valve, "drop", FALSE, NULL); // 打开阀门
        gst_object_unref(valve);
        m_photoPipelineActive = true;
        printf("拍照管道已激活\n");
    }
}

// 停用拍照管道
void CameraStream::deactivatePhotoPipeline()
{
    if (!m_photoPipelineActive) {
        return; // 已经停用
    }

    GstElement* valve = gst_bin_get_by_name(GST_BIN(pipeline), "photo_valve");
    if (valve) {
        g_object_set(valve, "drop", TRUE, NULL); // 关闭阀门
        gst_object_unref(valve);
        m_photoPipelineActive = false;
        qDebug() << "拍照管道已停用";
    }
}

// 拍照主函数
void CameraStream::takePhoto()
{
    if (!m_mainWindow) {
        qWarning() << "拍照失败: MainWindow未初始化";
        return;
    }

    // 检查摄像头连接状态
    if (!m_cameraConnected) {
        qWarning() << "拍照失败: 摄像头未连接，请等待摄像头重新连接";
        printf("拍照失败: 摄像头未连接\n");
        return;
    }

    // 检查摄像头是否已经初始化
    if (!pipeline || !tee) {
        qWarning() << "拍照失败: 摄像头未初始化或已断开，请重新连接摄像头";
        printf("拍照失败: 摄像头未初始化 (pipeline=%p, tee=%p)\n", pipeline, tee);
        return;
    }

    // 检查管道状态
    GstState state, pending;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &state, &pending, 0);
    if (ret == GST_STATE_CHANGE_FAILURE || state != GST_STATE_PLAYING) {
        qWarning() << "拍照失败: 摄像头管道状态异常，请重新连接摄像头";
        printf("拍照失败: 管道状态异常 (state=%s, pending=%s)\n",
               gst_element_state_get_name(state), gst_element_state_get_name(pending));
        return;
    }



    // 创建拍照分支（只一次）
    GstElement* appsink = createPhotoSink();
    if (!appsink) {
        printf("无法创建拍照分支，摄像头可能已断开\n");
        qWarning() << "拍照失败: 无法创建拍照分支，请检查摄像头连接";
        return;
    }

    // 设置拍照请求标志
    m_photoRequested = true;

    // 激活拍照管道，回调函数会自动处理拍照
    activatePhotoPipeline();

    printf("拍照请求已提交，等待回调处理...\n");
}

// 清理摄像头数据
void CameraStream::cleanupCameraData()
{
    printf("开始清理摄像头数据...\n");

    // 停止录像（如果正在录像）
    if (isRecording) {
        stopRecording();
    }

    // 销毁拍照分支
    if (m_photoBranchCreated) {
        GstElement* existing_sink = gst_bin_get_by_name(GST_BIN(pipeline), "photo_sink");
        if (existing_sink) {
            printf("清理拍照分支\n");
            destroyPhotoSink(existing_sink);
            gst_object_unref(existing_sink);
        }
    }

    // 重置拍照相关状态
    m_photoBranchCreated = false;
    m_photoRequested = false;
    m_photoPipelineActive = false;

    // 重置缩略图拍照相关状态
    m_thumbPhotoRequested = false;

    // 清理管道
    if (pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
        pipeline = nullptr;
    }

    // 重置其他元素指针
    tee = nullptr;
    flip = nullptr;
    parser = nullptr;
    decoder = nullptr;
    m_convert = nullptr;

    // 注意：不停止监控，让它继续检测摄像头重连
    printf("摄像头数据清理完成，监控继续运行等待重连\n");
}

// 检测摄像头设备是否存在
bool CameraStream::checkVideoDevice()
{
    // 查找最佳摄像头设备
    QString bestDevice = findBestCameraDevice();
    if (bestDevice.isEmpty()) {
        return false;
    }

    // 检查设备文件是否存在
    QFileInfo deviceFile(bestDevice);
    if (!deviceFile.exists()) {
        return false;
    }

    // 检查设备是否可读写
    if (!deviceFile.isReadable() || !deviceFile.isWritable()) {
        qDebug() << "设备文件权限不足:" << bestDevice;
        return false;
    }
    return true;
}

// 启动摄像头监控
void CameraStream::startCameraMonitoring()
{
    if (m_monitoringEnabled) {
        return; // 已经在监控中
    }

    printf("启动摄像头监控...\n");
    m_monitoringEnabled = true;

    // 每2秒检查一次摄像头状态
    m_cameraMonitorTimer->start(2000);
}

// 停止摄像头监控
void CameraStream::stopCameraMonitoring()
{
    if (!m_monitoringEnabled) {
        return; // 监控未启用
    }

    printf("停止摄像头监控...\n");
    m_monitoringEnabled = false;

    if (m_cameraMonitorTimer) {
        m_cameraMonitorTimer->stop();
    }
}

// 检查摄像头状态
void CameraStream::checkCameraStatus()
{
    if (!m_monitoringEnabled) {
        return;
    }

    // 检查设备文件是否存在
    bool deviceExists = checkVideoDevice();

    if (m_cameraConnected && !deviceExists) {
        // 摄像头已断开
        printf("检测到摄像头断开\n");
        handleCameraDisconnected();
    } else if (!m_cameraConnected && deviceExists) {
        // 摄像头重新连接
        printf("检测到摄像头重新连接\n");
        handleCameraReconnected();
    }
}

// 处理摄像头断开
void CameraStream::handleCameraDisconnected()
{
    if (!m_cameraConnected) {
        return; // 已经处理过断开
    }

    printf("处理摄像头断开事件...\n");
    m_cameraConnected = false;

    // 发出断开信号
    emit cameraDisconnected();

    // 清理摄像头资源
    cleanupCameraData();

    printf("摄像头断开处理完成，等待重新连接...\n");
}

// 处理摄像头重连
void CameraStream::handleCameraReconnected()
{
    if (m_cameraConnected) {
        return; // 已经连接
    }

    printf("处理摄像头重连事件...\n");

    // 检查设备是否支持4K60分辨率
    if (!checkResolutionSupport()) {
        printf("重连的设备不支持4K60分辨率，继续等待...\n");
        return;
    }

    // 尝试重新启动摄像头
    if (start_camera()) {
        printf("摄像头重连成功\n");
        m_cameraConnected = true;

        // 发出重连信号
        emit cameraReconnected();

        // 如果MainWindow存在，可以在这里通知UI更新
        if (m_mainWindow) {
            // 可以添加UI通知逻辑
        }
    } else {
        printf("摄像头重连失败，继续等待...\n");
    }
}

// 检测设备是否支持4K60分辨率
bool CameraStream::checkResolutionSupport()
{
    // 查找最佳摄像头设备
    QString bestDevice = findBestCameraDevice();
    if (bestDevice.isEmpty()) {
        qWarning() << "未找到摄像头设备，无法检测分辨率支持";
        return false;
    }

    // 使用v4l2-ctl命令检测设备支持的格式和分辨率
    QString command = QString("v4l2-ctl --list-formats-ext -d %1").arg(bestDevice);

    QProcess process;
    process.start(command);
    process.waitForFinished(5000); // 等待最多5秒

    if (process.exitCode() != 0) {
        qWarning() << "v4l2-ctl命令执行失败:" << process.errorString();
        return false;
    }

    QString output = process.readAllStandardOutput();

    // 检查输出中是否包含4K60支持
    // 查找MJPG格式和3840x2160分辨率
    bool hasMJPG = output.contains("MJPG", Qt::CaseInsensitive) ||
                   output.contains("Motion-JPEG", Qt::CaseInsensitive);
    bool has4K = output.contains("3840x2160") || output.contains("3840*2160");
    bool has60fps = false;

    if (hasMJPG && has4K) {
        // 进一步检查是否支持60fps
        QStringList lines = output.split('\n');
        bool found4KSection = false;

        for (const QString &line : lines) {
            // 查找包含3840x2160的行
            if (line.contains("3840x2160") || line.contains("3840*2160")) {
                found4KSection = true;
                continue;
            }

            // 在4K分辨率行之后查找帧率信息
            if (found4KSection && line.contains("fps")) {
                // 检查是否包含60fps或更高
                QRegExp fpsRegex("(\\d+\\.\\d+)\\s*fps");
                int pos = 0;
                while ((pos = fpsRegex.indexIn(line, pos)) != -1) {
                    double fps = fpsRegex.cap(1).toDouble();
                    if (fps >= 60.0) {
                        has60fps = true;
                        break;
                    }
                    pos += fpsRegex.matchedLength();
                }

                // 如果这一行没有更多的分辨率信息，说明4K部分结束了
                if (!line.contains("Size:") && !line.contains("Interval:")) {
                    break;
                }
            }
        }
    }

    bool supported = hasMJPG && has4K && has60fps;

    if (supported) {
        qDebug() << "设备支持4K60分辨率 (MJPG格式)";
    } else {
        qDebug() << "设备不支持4K60分辨率 - MJPG:" << hasMJPG << ", 4K:" << has4K << ", 60fps:" << has60fps;
    }

    return supported;
}

// USB 摄像头错误恢复
void CameraStream::handleUsbError()
{
    qWarning() << "检测到摄像头错误，尝试重新初始化...";
    printf("检测到摄像头错误，开始恢复程序...\n");

    // 标记摄像头为断开状态
    bool wasConnected = m_cameraConnected;
    m_cameraConnected = false;

    // 如果之前是连接状态，发出断开信号
    if (wasConnected) {
        emit cameraDisconnected();
    }

    // 完全清理当前摄像头资源
    cleanupCameraData();

    // 短暂延迟让设备稳定
    printf("等待设备稳定...\n");
    QThread::msleep(3000);

    // 检查设备是否可用
    if (!checkUsbCameraStatus()) {
        qWarning() << "摄像头设备不可用，启动监控等待设备重新连接";
        printf("摄像头设备不可用，启动监控等待设备重新连接\n");

        // 确保监控正在运行
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
        return;
    }

    // 检查设备是否支持4K60分辨率
    if (!checkResolutionSupport()) {
        qWarning() << "设备不支持4K60分辨率，启动监控等待合适的设备";
        printf("设备不支持4K60分辨率，启动监控等待合适的设备\n");

        // 确保监控正在运行
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
        return;
    }

    // 尝试重新启动摄像头
    printf("尝试重新启动摄像头...\n");
    if (start_camera()) {
        qDebug() << "摄像头错误恢复成功";
        printf("摄像头错误恢复成功\n");
        m_cameraConnected = true;

        // 通知主窗口摄像头已重连
        emit cameraReconnected();

        if (m_mainWindow) {
            // 可以添加UI通知逻辑
        }
    } else {
        qWarning() << "摄像头错误恢复失败，启动监控继续尝试";
        printf("摄像头错误恢复失败，启动监控继续尝试\n");
        m_cameraConnected = false;

        // 确保监控正在运行，继续尝试恢复
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
    }
}

// 检查 USB 摄像头状态
bool CameraStream::checkUsbCameraStatus()
{
    // 查找最佳摄像头设备
    QString bestDevice = findBestCameraDevice();
    if (bestDevice.isEmpty()) {
        qWarning() << "未找到任何USB摄像头设备";
        return false;
    }

    // 检查设备是否存在
    QFile videoDevice(bestDevice);
    if (!videoDevice.exists()) {
        qWarning() << "USB 摄像头设备" << bestDevice << "不存在";
        return false;
    }

    // 尝试打开设备检查状态
    if (videoDevice.open(QIODevice::ReadOnly)) {
        videoDevice.close();
        return true;
    } else {
        qWarning() << "无法访问 USB 摄像头设备" << bestDevice;
        return false;
    }
}

// GStreamer 总线消息回调
gboolean CameraStream::busCallback(GstBus *bus, GstMessage *message, gpointer data)
{
    CameraStream *stream = static_cast<CameraStream*>(data);

    switch (GST_MESSAGE_TYPE(message)) {
        case GST_MESSAGE_ERROR: {
            GError *error;
            gchar *debug;
            gst_message_parse_error(message, &error, &debug);

            qWarning() << "GStreamer 错误:" << error->message;
            qWarning() << "调试信息:" << debug;

            // 检查是否是摄像头相关错误
            QString errorMsg = QString(error->message);
            QString debugMsg = QString(debug ? debug : "");

            // 扩展错误检测范围，包括更多类型的摄像头错误
            bool isCameraError = errorMsg.contains("uvc", Qt::CaseInsensitive) ||
                               errorMsg.contains("USB", Qt::CaseInsensitive) ||
                               errorMsg.contains("device", Qt::CaseInsensitive) ||
                               errorMsg.contains("v4l2", Qt::CaseInsensitive) ||
                               errorMsg.contains("Internal data stream error", Qt::CaseInsensitive) ||
                               errorMsg.contains("streaming stopped", Qt::CaseInsensitive) ||
                               errorMsg.contains("No valid frames", Qt::CaseInsensitive) ||
                               debugMsg.contains("gst_base_src_loop", Qt::CaseInsensitive) ||
                               debugMsg.contains("gstvideodecoder", Qt::CaseInsensitive);

            if (isCameraError) {
                qWarning() << "检测到摄像头相关错误，启动恢复程序";
                printf("检测到摄像头相关错误: %s\n", qPrintable(errorMsg));
                printf("调试信息: %s\n", debug ? debug : "无");

                // 使用定时器延迟执行恢复，避免在回调中直接操作管道
                QTimer::singleShot(1000, stream, &CameraStream::handleUsbError);
            } else {
                qWarning() << "检测到非摄像头相关的GStreamer错误，不执行自动恢复";
            }

            g_error_free(error);
            g_free(debug);
            break;
        }
        case GST_MESSAGE_WARNING: {
            GError *warning;
            gchar *debug;
            gst_message_parse_warning(message, &warning, &debug);

            qWarning() << "GStreamer 警告:" << warning->message;

            g_error_free(warning);
            g_free(debug);
            break;
        }
        case GST_MESSAGE_EOS:
            qDebug() << "GStreamer EOS (End of Stream)";

            // 检查是否是意外的EOS（不是录像停止导致的）
            if (stream && stream->m_cameraConnected && !stream->m_expectingEOS) {
                qWarning() << "检测到意外的EOS，可能是摄像头流中断，启动恢复程序";

                // 使用定时器延迟执行恢复，避免在回调中直接操作管道
                QTimer::singleShot(1000, stream, [stream]() {
                    if (stream->m_cameraConnected && !stream->m_expectingEOS) {
                        stream->handleStreamEOS();
                    }
                });
            } else if (stream && stream->m_expectingEOS) {
                qDebug() << "收到期待的EOS（正常录像停止），无需恢复";
            }
            break;
        default:
            break;
    }

    return TRUE; // 继续处理消息
}

// 录像缩略图拍照
void CameraStream::takeThumbPhoto(const QString &recordingFileName)
{
    if (!m_mainWindow) {
        qWarning() << "缩略图拍照失败: MainWindow未初始化";
        return;
    }

    // 检查摄像头连接状态
    if (!m_cameraConnected) {
        qWarning() << "缩略图拍照失败: 摄像头未连接";
        printf("缩略图拍照失败: 摄像头未连接\n");
        return;
    }

    // 检查摄像头是否已经初始化
    if (!pipeline || !tee) {
        qWarning() << "缩略图拍照失败: 摄像头未初始化";
        printf("缩略图拍照失败: 摄像头未初始化 (pipeline=%p, tee=%p)\n", pipeline, tee);
        return;
    }

    // 检查管道状态，如果不是PLAYING状态则等待
    GstState state, pending;
    GstStateChangeReturn ret = gst_element_get_state(pipeline, &state, &pending, 0);

    if (state != GST_STATE_PLAYING) {
        printf("管道当前状态: %s, pending: %s, 等待切换到PLAYING状态...\n",
               gst_element_state_get_name(state), gst_element_state_get_name(pending));

        // 等待管道状态变为PLAYING，最多等待3秒
        ret = gst_element_get_state(pipeline, &state, &pending, 3 * GST_SECOND);

        if (ret == GST_STATE_CHANGE_FAILURE) {
            qWarning() << "缩略图拍照失败: 管道状态变化失败";
            printf("缩略图拍照失败: 管道状态变化失败\n");
            return;
        }

        if (state != GST_STATE_PLAYING) {
            qWarning() << "缩略图拍照失败: 管道未能切换到PLAYING状态";
            printf("缩略图拍照失败: 管道状态仍为 %s, pending: %s\n",
                   gst_element_state_get_name(state), gst_element_state_get_name(pending));
            return;
        }

        printf("管道已切换到PLAYING状态，继续缩略图拍照\n");
    }

    // 生成缩略图文件名（去掉.mp4扩展名，添加.thumb_前缀）
    QString baseFileName = recordingFileName;
    if (baseFileName.endsWith(".mp4")) {
        baseFileName = baseFileName.left(baseFileName.length() - 4);
    }
    m_thumbPhotoFileName = QString(".thumb_%1.jpg").arg(baseFileName);

    // 创建拍照分支（使用现有的拍照分支）
    GstElement* appsink = createPhotoSink();
    if (!appsink) {
        printf("无法创建拍照分支\n");
        qWarning() << "缩略图拍照失败: 无法创建拍照分支";
        return;
    }

    // 设置缩略图拍照请求标志
    m_thumbPhotoRequested = true;

    // 激活拍照管道，回调函数会自动处理拍照
    activatePhotoPipeline();

    printf("缩略图拍照请求已提交，文件名: %s\n", qPrintable(m_thumbPhotoFileName));
}

// 处理流结束 (EOS) 事件
void CameraStream::handleStreamEOS()
{
    qWarning() << "处理流结束事件，准备重新初始化摄像头...";
    printf("处理流结束事件，准备重新初始化摄像头...\n");

    // 标记摄像头为断开状态
    bool wasConnected = m_cameraConnected;
    m_cameraConnected = false;

    // 如果之前是连接状态，发出断开信号
    if (wasConnected) {
        emit cameraDisconnected();
    }

    // 完全清理当前摄像头资源
    cleanupCameraData();

    // 短暂延迟让系统稳定
    QThread::msleep(1000);

    // 检查设备是否仍然可用
    if (!checkVideoDevice()) {
        qWarning() << "摄像头设备不可用，启动监控等待设备重新连接";
        printf("摄像头设备不可用，启动监控等待设备重新连接\n");

        // 确保监控正在运行
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
        return;
    }

    // 检查设备是否支持4K60分辨率
    if (!checkResolutionSupport()) {
        qWarning() << "设备不支持4K60分辨率，启动监控等待合适的设备";
        printf("设备不支持4K60分辨率，启动监控等待合适的设备\n");

        // 确保监控正在运行
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
        return;
    }

    // 尝试重新启动摄像头
    printf("尝试重新启动摄像头...\n");
    if (start_camera()) {
        qDebug() << "摄像头EOS恢复成功";
        printf("摄像头EOS恢复成功\n");
        m_cameraConnected = true;

        // 发出重连信号
        emit cameraReconnected();

        // 通知主窗口摄像头已重连
        if (m_mainWindow) {
            // 可以添加UI通知逻辑
        }
    } else {
        qWarning() << "摄像头EOS恢复失败，启动监控继续尝试";
        printf("摄像头EOS恢复失败，启动监控继续尝试\n");
        m_cameraConnected = false;

        // 确保监控正在运行，继续尝试恢复
        if (!m_monitoringEnabled) {
            startCameraMonitoring();
        }
    }
}

// 强制重启摄像头
void CameraStream::forceRestartCamera()
{
    qWarning() << "强制重启摄像头...";
    printf("强制重启摄像头...\n");

    // 停止录像（如果正在录像）
    if (isRecording) {
        printf("停止当前录像...\n");
        stopRecording();
    }

    // 触发USB错误恢复流程
    handleUsbError();
}

// 查找最佳摄像头设备
QString CameraStream::findBestCameraDevice()
{
    CameraParams cameraParams;
    QList<QPair<QString, QString>> devices = cameraParams.findVideoDevices();

    // 优先查找video11
    for (const auto &device : devices) {
        if (device.second == "/dev/video11") {
            qDebug() << "找到video11设备:" << device.first;
            return device.second;
        }
    }

    // 如果没有video11，返回第一个可用设备
    if (!devices.isEmpty()) {
        qDebug() << "未找到video11，使用设备:" << devices.first().first << devices.first().second;
        return devices.first().second;
    }

    qDebug() << "未找到任何可用的摄像头设备";
    return QString();
}


